import requests
import hmac
import hashlib
import time
import json
from typing import Dict, Any, <PERSON><PERSON>


class MercadoBitcoinAPI:
    def __init__(self, api_key: str, api_secret: str):
        self.api_key = api_key
        self.api_secret = api_secret
        self.base_url = "https://api.mercadobitcoin.net/api/v4"

    def _generate_signature(self, method: str, path: str, body: str = "") -> Tuple[str, str]:
        """Gera assinatura HMAC-SHA256 para autenticação"""
        timestamp = str(int(time.time() * 1000))
        message = f"{timestamp}{method.upper()}{path}{body}"
        signature = hmac.new(
            self.api_secret.encode(), message.encode(), hashlib.sha256
        ).hexdigest()
        return timestamp, signature

    def _make_request(
        self, method: str, endpoint: str, data: Dict | None = None
    ) -> Dict[str, Any]:
        """Faz requisição autenticada para a API"""
        path = f"/api/v4{endpoint}"
        url = f"{self.base_url}{endpoint}"
        body = json.dumps(data) if data else ""

        timestamp, signature = self._generate_signature(method, path, body)
        # TODO parece estar desatualizado
        headers = {
            "MB-ACCESS-KEY": self.api_key,
            "MB-ACCESS-TIMESTAMP": timestamp,
            "MB-ACCESS-SIGNATURE": signature,
            "Content-Type": "application/json",
        }

        response = requests.request(method, url, headers=headers, data=body)
        return response.json()

    def get_account_balance(self) -> Dict[str, Any]:
        """Obtém saldo da conta"""
        return self._make_request("GET", "/accounts/balance")

    def get_ticker(self, symbol: str) -> Dict[str, Any]:
        """Obtém ticker de um par"""
        return self._make_request("GET", f"/symbols/{symbol}/ticker")

    def place_order(
        self, symbol: str, side: str, type_order: str, quantity: str, price: str | None = None
    ) -> Dict[str, Any]:
        """Coloca uma ordem"""
        data = {
            "symbol": symbol,
            "side": side,  # "buy" ou "sell"
            "type": type_order,  # "market" ou "limit"
            "quantity": quantity,
        }
        if price and type_order == "limit":
            data["price"] = price

        return self._make_request("POST", "/orders", data)

    def get_orders(self, symbol: str | None = None, status: str | None = None) -> Dict[str, Any]:
        """Lista ordens"""
        endpoint = "/orders"
        params = []
        if symbol:
            params.append(f"symbol={symbol}")
        if status:
            params.append(f"status={status}")
        if params:
            endpoint += "?" + "&".join(params)

        return self._make_request("GET", endpoint)
