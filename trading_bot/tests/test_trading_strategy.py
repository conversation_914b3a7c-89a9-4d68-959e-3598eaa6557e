import pytest
from trading_strategy import SimpleMovingAverageStrategy

class TestSimpleMovingAverageStrategy:
    
    @pytest.fixture
    def strategy(self):
        """Estratégia para testes"""
        return SimpleMovingAverageStrategy(short_period=3, long_period=5)
    
    def test_init(self):
        """Testa inicialização da estratégia"""
        strategy = SimpleMovingAverageStrategy(short_period=10, long_period=20)
        
        assert strategy.short_period == 10
        assert strategy.long_period == 20
        assert strategy.price_history == []
    
    def test_update_price_history(self, strategy):
        """Testa atualização do histórico de preços"""
        prices = [100, 101, 102, 103, 104, 105, 106]
        
        for price in prices:
            strategy.update_price_history(price)
        
        # Deve manter apenas os últimos 5 preços (long_period)
        assert len(strategy.price_history) == 5
        assert strategy.price_history == [102, 103, 104, 105, 106]
    
    def test_calculate_sma_insufficient_data(self, strategy):
        """Testa SMA com dados insuficientes"""
        strategy.price_history = [100, 101]
        
        sma = strategy._calculate_sma(3)
        assert sma == 0
    
    def test_calculate_sma_sufficient_data(self, strategy):
        """Testa cálculo de SMA com dados suficientes"""
        strategy.price_history = [100, 102, 104, 106, 108]
        
        short_sma = strategy._calculate_sma(3)  # (104+106+108)/3 = 106
        long_sma = strategy._calculate_sma(5)   # (100+102+104+106+108)/5 = 104
        
        assert short_sma == 106.0
        assert long_sma == 104.0
    
    def test_should_buy_insufficient_history(self, strategy):
        """Testa sinal de compra com histórico insuficiente"""
        strategy.price_history = [100, 101, 102]  # Menos que long_period
        
        should_buy = strategy.should_buy({"price": 103})
        assert should_buy is False
    
    def test_should_buy_true(self, strategy):
        """Testa sinal de compra verdadeiro (SMA curta > SMA longa)"""
        # Preços em tendência de alta
        strategy.price_history = [100, 101, 102, 105, 108]
        
        should_buy = strategy.should_buy({"price": 108})
        assert should_buy is True
    
    def test_should_buy_false(self, strategy):
        """Testa sinal de compra falso (SMA curta <= SMA longa)"""
        # Preços em tendência de baixa
        strategy.price_history = [108, 105, 102, 101, 100]
        
        should_buy = strategy.should_buy({"price": 100})
        assert should_buy is False
    
    def test_should_sell_true(self, strategy):
        """Testa sinal de venda verdadeiro (SMA curta < SMA longa)"""
        # Preços em tendência de baixa
        strategy.price_history = [108, 105, 102, 101, 100]
        
        should_sell = strategy.should_sell({"price": 100})
        assert should_sell is True
    
    def test_should_sell_false(self, strategy):
        """Testa sinal de venda falso (SMA curta >= SMA longa)"""
        # Preços em tendência de alta
        strategy.price_history = [100, 101, 102, 105, 108]
        
        should_sell = strategy.should_sell({"price": 108})
        assert should_sell is False
    
    def test_calculate_quantity(self, strategy):
        """Testa cálculo de quantidade"""
        balance = 1000.0
        price = 50000.0
        
        quantity = strategy.calculate_quantity(balance, price)
        expected = (1000.0 * 0.1) / 50000.0  # 10% do saldo
        
        assert quantity == f"{expected:.8f}"
        assert quantity == "0.00200000"