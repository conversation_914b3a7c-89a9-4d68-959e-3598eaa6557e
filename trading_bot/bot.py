import time
import logging
from typing import Dict, Any
from mercado_bitcoin_api import MercadoBitcoinAPI
from trading_strategy import TradingStrategy


class TradingBot:
    def __init__(
        self, api: MercadoBitcoinAPI, strategy: TradingStrategy, symbol: str = "BTC-BRL"
    ):
        self.api = api
        self.strategy = strategy
        self.symbol = symbol
        self.is_running = False
        self.position = None  # "long", "short", ou None

        # Configurar logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    def get_current_price(self) -> float:
        """Obtém preço atual do par"""
        ticker = self.api.get_ticker(self.symbol)
        return float(ticker.get("last", 0))

    def get_balance(self, currency: str = "BRL") -> float:
        """Obtém saldo de uma moeda"""
        balance = self.api.get_account_balance()
        for account in balance.get("accounts", []):
            if account.get("currency") == currency:
                return float(account.get("available", 0))
        return 0.0

    def execute_buy_order(self, price: float):
        """Executa ordem de compra"""
        balance = self.get_balance("BRL")
        if balance < 50:  # Mínimo para operar
            self.logger.warning("Saldo insuficiente para compra")
            return

        quantity = self.strategy.calculate_quantity(balance, price)

        try:
            order = self.api.place_order(
                symbol=self.symbol, side="buy", type_order="market", quantity=quantity
            )
            self.logger.info(f"Ordem de compra executada: {order}")
            self.position = "long"
        except Exception as e:
            self.logger.error(f"Erro ao executar compra: {e}")

    def execute_sell_order(self):
        """Executa ordem de venda"""
        btc_balance = self.get_balance("BTC")
        if btc_balance < 0.00001:  # Mínimo para vender
            self.logger.warning("Sem BTC para vender")
            return

        try:
            order = self.api.place_order(
                symbol=self.symbol,
                side="sell",
                type_order="market",
                quantity=f"{btc_balance:.8f}",
            )
            self.logger.info(f"Ordem de venda executada: {order}")
            self.position = None
        except Exception as e:
            self.logger.error(f"Erro ao executar venda: {e}")

    def run(self, interval: int = 60):
        """Executa o bot com intervalo especificado (segundos)"""
        self.is_running = True
        self.logger.info(f"Bot iniciado para {self.symbol}")

        while self.is_running:
            try:
                current_price = self.get_current_price()
                self.logger.info(f"Preço atual: R$ {current_price}")

                # Atualizar estratégia com novo preço
                if hasattr(self.strategy, "update_price_history"):
                    self.strategy.update_price_history(current_price)

                market_data = {"price": current_price}

                # Verificar sinais de compra/venda
                if self.position is None and self.strategy.should_buy(market_data):
                    self.logger.info("Sinal de compra detectado")
                    self.execute_buy_order(current_price)
                elif self.position == "long" and self.strategy.should_sell(market_data):
                    self.logger.info("Sinal de venda detectado")
                    self.execute_sell_order()

                time.sleep(interval)

            except KeyboardInterrupt:
                self.logger.info("Bot interrompido pelo usuário")
                self.stop()
            except Exception as e:
                self.logger.error(f"Erro no loop principal: {e}")
                time.sleep(interval)

    def stop(self):
        """Para o bot"""
        self.is_running = False
        self.logger.info("Bot parado")
