from abc import ABC, abstractmethod
from typing import Dict, Any
import logging


class TradingStrategy(ABC):
    """Classe base para estratégias de trading"""

    @abstractmethod
    def should_buy(self, market_data: Dict[str, Any]) -> bool:
        pass

    @abstractmethod
    def should_sell(self, market_data: Dict[str, Any]) -> bool:
        pass

    @abstractmethod
    def calculate_quantity(self, balance: float, price: float) -> str:
        pass


class SimpleMovingAverageStrategy(TradingStrategy):
    """Estratégia baseada em média móvel simples"""

    def __init__(self, short_period: int = 10, long_period: int = 30):
        self.short_period = short_period
        self.long_period = long_period
        self.price_history = []

    def update_price_history(self, price: float):
        """Atualiza histórico de preços"""
        self.price_history.append(price)
        if len(self.price_history) > self.long_period:
            self.price_history.pop(0)

    def _calculate_sma(self, period: int) -> float:
        """Calcula média móvel simples"""
        if len(self.price_history) < period:
            return 0
        return sum(self.price_history[-period:]) / period

    def should_buy(self, market_data: Dict[str, Any]) -> bool:
        """Compra quando SMA curta cruza acima da SMA longa"""
        if len(self.price_history) < self.long_period:
            return False

        short_sma = self._calculate_sma(self.short_period)
        long_sma = self._calculate_sma(self.long_period)

        return short_sma > long_sma

    def should_sell(self, market_data: Dict[str, Any]) -> bool:
        """Vende quando SMA curta cruza abaixo da SMA longa"""
        if len(self.price_history) < self.long_period:
            return False

        short_sma = self._calculate_sma(self.short_period)
        long_sma = self._calculate_sma(self.long_period)

        return short_sma < long_sma

    def calculate_quantity(self, balance: float, price: float) -> str:
        """Calcula quantidade baseada em 10% do saldo"""
        quantity = (balance * 0.1) / price
        return f"{quantity:.8f}"
