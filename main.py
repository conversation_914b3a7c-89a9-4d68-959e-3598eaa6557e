from trading_bot.mercado_bitcoin_api import MercadoBitcoinAPI
from trading_bot.trading_strategy import SimpleMovingAverageStrategy
from trading_bot.bot import TradingBot
import os


def main():
    # Configurar credenciais (use variáveis de ambiente)
    api_key = os.getenv("MB_API_KEY")
    api_secret = os.getenv("MB_API_SECRET")

    if not api_key or not api_secret:
        print("Configure as variáveis MB_API_KEY e MB_API_SECRET")
        return

    # Inicializar API
    api = MercadoBitcoinAPI(api_key, api_secret)

    # Configurar estratégia
    strategy = SimpleMovingAverageStrategy(short_period=10, long_period=30)

    # Criar e executar bot
    bot = TradingBot(api, strategy, symbol="BTC-BRL")

    try:
        bot.run(interval=300)  # Executa a cada 5 minutos
    except KeyboardInterrupt:
        bot.stop()


if __name__ == "__main__":
    main()
